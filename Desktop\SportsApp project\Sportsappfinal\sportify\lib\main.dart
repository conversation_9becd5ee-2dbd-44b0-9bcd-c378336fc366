import 'package:flutter/material.dart';
import 'package:sportify/features/home/<USER>/home_screen.dart';
import 'package:sportify/features/splash/presentation/splash_screen.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Sportify',
      debugShowCheckedModeBanner: false,
      home: SplashScreen(),
      routes: {'/home': (context) => const HomeScreen()},
    );
  }
}
