import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final phoneController = TextEditingController();
  final otpController = TextEditingController();
  String generatedOtp = '';
  bool isVerifying = false;

  void generateOtp() {
    final random = Random();
    final otp = (1000 + random.nextInt(9000))
        .toString(); // Generate 4-digit OTP
        // TODO: Send OTP to user's phone number
    setState(() => generatedOtp = otp);

    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Your OTP'),
        content: Text('Use this OTP: $generatedOtp'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void verifyOtp() async {
    if (otpController.text.trim() != generatedOtp) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Incorrect OTP')));
      return;
    }

    setState(() => isVerifying = true);

    await Future.delayed(const Duration(seconds: 3)); // Simulate loading

    setState(() => isVerifying = false);

    Navigator.pushReplacementNamed(context, '/home'); // Navigate to home
  }

  void loginWithGoogle() async {
    final GoogleSignIn _googleSignIn = GoogleSignIn();

    try {
      final account = await _googleSignIn.signIn();
      if (account != null) {
        // احفظ الاسم الكامل في التخزين المؤقت أو حالة المستخدم
        final name = account.displayName ?? 'No Name';
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Welcome $name')));

        Navigator.pushReplacementNamed(context, '/home');
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Google Sign-In failed')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            const SizedBox(height: 40),
            TextField(
              controller: phoneController,
              keyboardType: TextInputType.phone,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: otpController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'OTP',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: generateOtp,
                    child: const Text('Generate OTP'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: isVerifying
                      ? const Center(child: CircularProgressIndicator())
                      : ElevatedButton(
                          onPressed: verifyOtp,
                          child: const Text('Verify OTP'),
                        ),
                ),
              ],
            ),
            const SizedBox(height: 32),
            const Divider(),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: loginWithGoogle,
              icon: const Icon(Icons.login),
              label: const Text('Login with Google'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.black,
                elevation: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
