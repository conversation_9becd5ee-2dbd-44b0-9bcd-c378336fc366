import 'package:flutter/material.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sportify'),
        backgroundColor: const Color.fromARGB(255, 43, 125, 239),
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.sports_soccer,
              size: 100,
              color: Color.fromARGB(255, 43, 125, 239),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            Text(
              'Welcome to Sportify!',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Sized<PERSON><PERSON>(height: 10),
            Text(
              'Your Daily Dose of Sports News',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
